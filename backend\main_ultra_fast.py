"""
🚀 SERVEUR ULTRA-RAPIDE ICOM IC-R8600
Interface WebSocket + REST optimisée pour latence minimale
Contrôle instantané pour guerre électronique
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Set
from contextlib import asynccontextmanager

from fastapi import Fast<PERSON>I, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from icom_handler import ICOMHandler

# ============================================================================
# MODÈLES ULTRA-RAPIDES
# ============================================================================

class FastCommand(BaseModel):
    action: str
    data: Dict = {}
    timestamp: float = None

class FastResponse(BaseModel):
    success: bool
    data: Dict = {}
    latency_ms: float = 0
    timestamp: float = None

class RadioStatus(BaseModel):
    frequency: int = 145000000
    mode: str = "FM"
    rf_gain: int = 50
    rssi: int = -80
    power_on: bool = False
    filter_width: int = 15000
    timestamp: float = None

# ============================================================================
# GESTIONNAIRE DE CONNEXIONS WEBSOCKET
# ============================================================================

class ConnectionManager:
    def __init__(self):
        self.active_connections: Set[WebSocket] = set()
        self.last_status: Dict = {}
        self.status_lock = asyncio.Lock()

    async def connect(self, websocket: WebSocket):
        self.active_connections.add(websocket)
        logging.info(f"🔗 Nouvelle connexion WebSocket. Total: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        self.active_connections.discard(websocket)
        logging.info(f"❌ Connexion WebSocket fermée. Total: {len(self.active_connections)}")

    async def broadcast_status(self, status: Dict):
        if not self.active_connections:
            return

        async with self.status_lock:
            # Éviter les broadcasts inutiles
            if status == self.last_status:
                return
            self.last_status = status.copy()

        message = {
            "type": "status",
            "data": status,
            "timestamp": time.time()
        }

        # Broadcast ultra-rapide
        disconnected = set()
        for connection in self.active_connections:
            try:
                await connection.send_text(json.dumps(message))
            except Exception:
                disconnected.add(connection)

        # Nettoyage des connexions fermées
        for conn in disconnected:
            self.active_connections.discard(conn)

# ============================================================================
# CONFIGURATION GLOBALE
# ============================================================================

# Variables globales
icom_handler: Optional[ICOMHandler] = None
connection_manager = ConnectionManager()
status_task: Optional[asyncio.Task] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Gestionnaire de cycle de vie ultra-optimisé"""
    global icom_handler, status_task
    
    # DÉMARRAGE ULTRA-RAPIDE
    logging.info("🚀 Démarrage serveur ultra-rapide...")
    
    # Initialisation ICOM avec configuration optimisée pour VirtualHere
    icom_handler = ICOMHandler(
        port="COM6",
        baudrate=19200,
        use_udp=False,  # Connexion série via VirtualHere USB Server
        udp_host="************",  # PC avec VirtualHere Server
        udp_port=50001
    )
    
    # Connexion immédiate
    if icom_handler.connect():
        logging.info("✅ ICOM connecté - Mode ultra-rapide activé")
    else:
        logging.warning("⚠️ ICOM non connecté - Mode simulation")
    
    # Démarrage de la boucle de statut ultra-rapide
    status_task = asyncio.create_task(ultra_fast_status_loop())
    
    yield
    
    # ARRÊT PROPRE
    logging.info("🛑 Arrêt serveur...")
    if status_task:
        status_task.cancel()
    if icom_handler:
        icom_handler.disconnect()

# ============================================================================
# APPLICATION FASTAPI
# ============================================================================

app = FastAPI(
    title="🚀 ICOM IC-R8600 ULTRA-FAST Controller",
    description="Interface ultra-rapide pour guerre électronique",
    version="2.0.0-ULTRA",
    lifespan=lifespan
)

# CORS ultra-permissif pour développement
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration logging optimisée
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

# ============================================================================
# BOUCLE DE STATUT ULTRA-RAPIDE
# ============================================================================

async def ultra_fast_status_loop():
    """Boucle de mise à jour du statut optimisée"""
    while True:
        try:
            if icom_handler and connection_manager.active_connections:
                start_time = time.time()

                # Lecture du statut avec gestion d'erreur
                try:
                    status = icom_handler.get_status()
                    status["timestamp"] = time.time()
                    status["latency_ms"] = (time.time() - start_time) * 1000

                    # Broadcast seulement si on a des données valides
                    await connection_manager.broadcast_status(status)
                except Exception as e:
                    # En cas d'erreur, envoyer un statut par défaut
                    default_status = {
                        "frequency": 145000000,
                        "mode": "FM",
                        "rf_gain": 50,
                        "rssi": -80,
                        "power_on": True,
                        "timestamp": time.time(),
                        "latency_ms": (time.time() - start_time) * 1000,
                        "simulation_mode": True
                    }
                    await connection_manager.broadcast_status(default_status)

            # Attente 200ms pour 5Hz (plus stable)
            await asyncio.sleep(0.2)

        except Exception as e:
            logging.error(f"Erreur boucle statut: {e}")
            await asyncio.sleep(1.0)

# ============================================================================
# WEBSOCKET ULTRA-RAPIDE
# ============================================================================

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket ultra-rapide pour contrôle temps réel"""
    # Headers WebSocket pour compatibilité
    await websocket.accept()
    await connection_manager.connect(websocket)
    
    try:
        while True:
            # Réception commande
            data = await websocket.receive_text()
            start_time = time.time()
            
            try:
                command = json.loads(data)
                response = await process_fast_command(command)
                response["latency_ms"] = (time.time() - start_time) * 1000
                
                await websocket.send_text(json.dumps(response))
                
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "success": False,
                    "error": "JSON invalide",
                    "timestamp": time.time()
                }))
                
    except WebSocketDisconnect:
        connection_manager.disconnect(websocket)

# ============================================================================
# TRAITEMENT COMMANDES ULTRA-RAPIDE
# ============================================================================

async def process_fast_command(command: Dict) -> Dict:
    """Traitement ultra-rapide des commandes"""
    if not icom_handler:
        return {"success": False, "error": "ICOM non connecté"}
    
    action = command.get("action")
    data = command.get("data", {})
    
    try:
        if action == "frequency":
            freq = data.get("frequency")
            success = icom_handler.set_frequency(freq)
            return {"success": success, "action": action, "frequency": freq}
            
        elif action == "mode":
            mode = data.get("mode")
            success = icom_handler.set_mode(mode)
            return {"success": success, "action": action, "mode": mode}
            
        elif action == "rf_gain":
            gain = data.get("gain")
            success = icom_handler.set_rf_gain(gain)
            return {"success": success, "action": action, "rf_gain": gain}
            
        elif action == "power":
            state = data.get("state")
            if state:
                success = icom_handler.power_on()
            else:
                success = icom_handler.power_off()
            return {"success": success, "action": action, "power_on": state}
            
        else:
            return {"success": False, "error": f"Action inconnue: {action}"}
            
    except Exception as e:
        return {"success": False, "error": str(e)}

# ============================================================================
# ROUTES REST ULTRA-RAPIDES
# ============================================================================

@app.get("/")
async def root():
    return {
        "service": "🚀 ICOM IC-R8600 ULTRA-FAST",
        "version": "2.0.0-ULTRA",
        "status": "OPERATIONAL",
        "connections": len(connection_manager.active_connections),
        "timestamp": time.time()
    }

@app.post("/frequency")
async def set_frequency_fast(data: Dict):
    """Changement fréquence ultra-rapide"""
    start_time = time.time()
    
    if not icom_handler:
        raise HTTPException(status_code=503, detail="ICOM non connecté")
    
    freq = data.get("frequency")
    if not freq:
        raise HTTPException(status_code=400, detail="Fréquence requise")
    
    success = icom_handler.set_frequency(freq)
    latency = (time.time() - start_time) * 1000
    
    return {
        "success": success,
        "frequency": freq,
        "latency_ms": latency,
        "timestamp": time.time()
    }

@app.post("/mode")
async def set_mode_fast(data: Dict):
    """Changement mode ultra-rapide"""
    start_time = time.time()
    
    if not icom_handler:
        raise HTTPException(status_code=503, detail="ICOM non connecté")
    
    mode = data.get("mode")
    if not mode:
        raise HTTPException(status_code=400, detail="Mode requis")
    
    success = icom_handler.set_mode(mode)
    latency = (time.time() - start_time) * 1000
    
    return {
        "success": success,
        "mode": mode,
        "latency_ms": latency,
        "timestamp": time.time()
    }

@app.post("/rf_gain")
async def set_rf_gain_fast(data: Dict):
    """Changement RF gain ultra-rapide"""
    start_time = time.time()
    
    if not icom_handler:
        raise HTTPException(status_code=503, detail="ICOM non connecté")
    
    gain = data.get("gain")
    if gain is None:
        raise HTTPException(status_code=400, detail="Gain requis")
    
    success = icom_handler.set_rf_gain(gain)
    latency = (time.time() - start_time) * 1000
    
    return {
        "success": success,
        "rf_gain": gain,
        "latency_ms": latency,
        "timestamp": time.time()
    }

@app.post("/power")
async def set_power_fast(data: Dict):
    """Contrôle alimentation ultra-rapide"""
    start_time = time.time()
    
    if not icom_handler:
        raise HTTPException(status_code=503, detail="ICOM non connecté")
    
    state = data.get("state")
    if state is None:
        raise HTTPException(status_code=400, detail="État requis")
    
    if state:
        success = icom_handler.power_on()
    else:
        success = icom_handler.power_off()
    
    latency = (time.time() - start_time) * 1000
    
    return {
        "success": success,
        "power_on": state,
        "latency_ms": latency,
        "timestamp": time.time()
    }

@app.get("/status")
async def get_status_fast():
    """Statut ultra-rapide"""
    start_time = time.time()
    
    if not icom_handler:
        raise HTTPException(status_code=503, detail="ICOM non connecté")
    
    status = icom_handler.get_status()
    latency = (time.time() - start_time) * 1000
    
    return {
        **status,
        "latency_ms": latency,
        "timestamp": time.time()
    }

if __name__ == "__main__":
    import uvicorn
    import sys

    # Configuration optimisée selon la plateforme
    loop_type = "auto"  # uvloop sur Linux/Mac, asyncio sur Windows
    if sys.platform == "win32":
        loop_type = "asyncio"

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        access_log=False,  # Désactiver logs d'accès pour performance
        loop=loop_type  # Boucle événements optimisée
    )
