import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { 
  Power, 
  Radio, 
  Volume2, 
  Settings, 
  Play, 
  Square, 
  Download,
  Trash2,
  RefreshCw,
  Zap,
  Activity
} from 'lucide-react';

// Configuration de l'API pour VirtualHere USB Server
const API_BASE_URL = 'http://192.168.37.10:8000';

// Styles CSS-in-JS
const styles = {
  container: {
    minHeight: '100vh',
    padding: '20px',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  },
  
  header: {
    textAlign: 'center',
    color: 'white',
    marginBottom: '30px',
  },
  
  title: {
    fontSize: '2.5rem',
    fontWeight: 'bold',
    margin: '0 0 10px 0',
    textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
  },
  
  subtitle: {
    fontSize: '1.2rem',
    opacity: 0.9,
    margin: 0,
  },
  
  mainGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
    gap: '20px',
    maxWidth: '1400px',
    margin: '0 auto',
  },
  
  card: {
    background: 'rgba(255, 255, 255, 0.95)',
    borderRadius: '15px',
    padding: '25px',
    boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(255,255,255,0.2)',
  },
  
  cardTitle: {
    fontSize: '1.4rem',
    fontWeight: 'bold',
    marginBottom: '20px',
    color: '#333',
    display: 'flex',
    alignItems: 'center',
    gap: '10px',
  },
  
  formGroup: {
    marginBottom: '20px',
  },
  
  label: {
    display: 'block',
    marginBottom: '8px',
    fontWeight: '600',
    color: '#555',
  },
  
  input: {
    width: '100%',
    padding: '12px',
    border: '2px solid #e1e5e9',
    borderRadius: '8px',
    fontSize: '16px',
    transition: 'border-color 0.3s ease',
  },
  
  select: {
    width: '100%',
    padding: '12px',
    border: '2px solid #e1e5e9',
    borderRadius: '8px',
    fontSize: '16px',
    backgroundColor: 'white',
    cursor: 'pointer',
  },
  
  button: {
    padding: '12px 24px',
    border: 'none',
    borderRadius: '8px',
    fontSize: '16px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    justifyContent: 'center',
  },
  
  buttonPrimary: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
  },
  
  buttonSuccess: {
    background: 'linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%)',
    color: 'white',
  },
  
  buttonDanger: {
    background: 'linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%)',
    color: 'white',
  },
  
  buttonGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
    gap: '10px',
    marginTop: '20px',
  },
  
  statusGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
    gap: '15px',
  },
  
  statusItem: {
    background: '#f8f9fa',
    padding: '15px',
    borderRadius: '8px',
    textAlign: 'center',
    border: '2px solid #e9ecef',
  },
  
  statusLabel: {
    fontSize: '0.9rem',
    color: '#6c757d',
    marginBottom: '5px',
  },
  
  statusValue: {
    fontSize: '1.2rem',
    fontWeight: 'bold',
    color: '#333',
  },
  
  alert: {
    padding: '15px',
    borderRadius: '8px',
    marginBottom: '20px',
    border: '1px solid',
  },
  
  alertSuccess: {
    backgroundColor: '#d4edda',
    borderColor: '#c3e6cb',
    color: '#155724',
  },
  
  alertError: {
    backgroundColor: '#f8d7da',
    borderColor: '#f5c6cb',
    color: '#721c24',
  },
};

const App = () => {
  // États pour les contrôles
  const [frequency, setFrequency] = useState(145500000); // 145.5 MHz par défaut
  const [mode, setMode] = useState('FM');
  const [rfGain, setRfGain] = useState(128);
  const [audioType, setAudioType] = useState('AF');
  
  // États pour le scan
  const [scanStartFreq, setScanStartFreq] = useState(144000000);
  const [scanEndFreq, setScanEndFreq] = useState(146000000);
  const [scanStep, setScanStep] = useState(25000);
  
  // États pour l'état du récepteur
  const [radioStatus, setRadioStatus] = useState({
    frequency: 0,
    mode: 'FM',
    rssi: 0,
    power_on: false,
    rf_gain: 128,
    filter_width: 0
  });
  
  // États pour l'enregistrement
  const [isRecording, setIsRecording] = useState(false);
  const [recordings, setRecordings] = useState([]);
  
  // États pour les messages
  const [message, setMessage] = useState({ type: '', text: '' });
  const [loading, setLoading] = useState(false);

  // Modes de modulation disponibles
  const modes = ['LSB', 'USB', 'AM', 'CW', 'FM', 'WFM', 'CWR', 'RTTY', 'RTTYR', 'PSK', 'PSKR'];

  // Fonction pour afficher un message
  const showMessage = (type, text) => {
    setMessage({ type, text });
    setTimeout(() => setMessage({ type: '', text: '' }), 5000);
  };

  // Fonction pour formater la fréquence
  const formatFrequency = (freq) => {
    if (freq >= 1000000) {
      return `${(freq / 1000000).toFixed(3)} MHz`;
    } else if (freq >= 1000) {
      return `${(freq / 1000).toFixed(1)} kHz`;
    }
    return `${freq} Hz`;
  };

  // Fonction pour formater la durée
  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Fonctions API
  const sendCommand = async (commandData) => {
    setLoading(true);
    try {
      const response = await axios.post(`${API_BASE_URL}/api/command`, commandData);
      showMessage('success', response.data.message);
      await getStatus(); // Mettre à jour l'état
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur de communication');
    } finally {
      setLoading(false);
    }
  };

  const getStatus = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/status`);
      setRadioStatus(response.data);
    } catch (error) {
      console.error('Erreur lecture état:', error);
    }
  };

  const startScan = async () => {
    setLoading(true);
    try {
      const response = await axios.post(`${API_BASE_URL}/api/scan/start`, {
        start_frequency: scanStartFreq,
        end_frequency: scanEndFreq,
        step: scanStep,
        mode: mode
      });
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur scan');
    } finally {
      setLoading(false);
    }
  };

  const stopScan = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/scan/stop`);
      showMessage('success', response.data.message);
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur arrêt scan');
    }
  };

  const startRecording = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/audio/start`, {
        audio_type: audioType
      });
      setIsRecording(true);
      showMessage('success', response.data.message);
      await loadRecordings();
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur enregistrement');
    }
  };

  const stopRecording = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/audio/stop`);
      setIsRecording(false);
      showMessage('success', response.data.message);
      await loadRecordings();
    } catch (error) {
      showMessage('error', error.response?.data?.detail || 'Erreur arrêt enregistrement');
    }
  };

  const loadRecordings = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/recordings`);
      setRecordings(response.data);
    } catch (error) {
      console.error('Erreur chargement enregistrements:', error);
    }
  };

  const deleteRecording = async (filename) => {
    try {
      await axios.delete(`${API_BASE_URL}/api/recordings/${filename}`);
      showMessage('success', 'Enregistrement supprimé');
      await loadRecordings();
    } catch (error) {
      showMessage('error', 'Erreur suppression');
    }
  };

  // Gestionnaires d'événements
  const handleSendCommand = () => {
    sendCommand({
      frequency: frequency,
      mode: mode,
      rf_gain: rfGain
    });
  };

  const handlePowerOn = () => {
    sendCommand({ power_on: true });
  };

  const handlePowerOff = () => {
    sendCommand({ power_on: false });
  };

  // Effet pour charger les données au démarrage
  useEffect(() => {
    getStatus();
    loadRecordings();

    // Polling pour l'état du récepteur
    const interval = setInterval(getStatus, 2000);
    return () => clearInterval(interval);
  }, []);

  // Rendu du composant
  return (
    <div style={styles.container}>
      {/* En-tête */}
      <div style={styles.header}>
        <h1 style={styles.title}>
          <Radio size={40} style={{ marginRight: '15px' }} />
          ICOM IC-R8600 Controller
        </h1>
        <p style={styles.subtitle}>
          Interface de contrôle pour récepteur radiofréquence
        </p>
      </div>

      {/* Messages d'alerte */}
      {message.text && (
        <div style={{
          ...styles.alert,
          ...(message.type === 'success' ? styles.alertSuccess : styles.alertError)
        }}>
          {message.text}
        </div>
      )}

      {/* Grille principale */}
      <div style={styles.mainGrid}>

        {/* Carte de contrôle principal */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <Settings size={24} />
            Contrôles Principal
          </h2>

          <div style={styles.formGroup}>
            <label style={styles.label}>Fréquence (Hz)</label>
            <input
              type="number"
              value={frequency}
              onChange={(e) => setFrequency(parseInt(e.target.value))}
              style={styles.input}
              placeholder="145500000"
            />
          </div>

          <div style={styles.formGroup}>
            <label style={styles.label}>Mode de modulation</label>
            <select
              value={mode}
              onChange={(e) => setMode(e.target.value)}
              style={styles.select}
            >
              {modes.map(m => (
                <option key={m} value={m}>{m}</option>
              ))}
            </select>
          </div>

          <div style={styles.formGroup}>
            <label style={styles.label}>RF Gain (0-255)</label>
            <input
              type="range"
              min="0"
              max="255"
              value={rfGain}
              onChange={(e) => setRfGain(parseInt(e.target.value))}
              style={styles.input}
            />
            <div style={{ textAlign: 'center', marginTop: '5px' }}>
              {rfGain}
            </div>
          </div>

          <div style={styles.buttonGrid}>
            <button
              onClick={handleSendCommand}
              disabled={loading}
              style={{...styles.button, ...styles.buttonPrimary}}
            >
              <Zap size={20} />
              Envoyer
            </button>

            <button
              onClick={handlePowerOn}
              disabled={loading}
              style={{...styles.button, ...styles.buttonSuccess}}
            >
              <Power size={20} />
              ON
            </button>

            <button
              onClick={handlePowerOff}
              disabled={loading}
              style={{...styles.button, ...styles.buttonDanger}}
            >
              <Power size={20} />
              OFF
            </button>
          </div>
        </div>

        {/* Carte d'état du récepteur */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <Activity size={24} />
            État du Récepteur
          </h2>

          <div style={styles.statusGrid}>
            <div style={styles.statusItem}>
              <div style={styles.statusLabel}>Fréquence</div>
              <div style={styles.statusValue}>
                {formatFrequency(radioStatus.frequency)}
              </div>
            </div>

            <div style={styles.statusItem}>
              <div style={styles.statusLabel}>Mode</div>
              <div style={styles.statusValue}>{radioStatus.mode}</div>
            </div>

            <div style={styles.statusItem}>
              <div style={styles.statusLabel}>RSSI</div>
              <div style={styles.statusValue}>{radioStatus.rssi}</div>
            </div>

            <div style={styles.statusItem}>
              <div style={styles.statusLabel}>Alimentation</div>
              <div style={{
                ...styles.statusValue,
                color: radioStatus.power_on ? '#28a745' : '#dc3545'
              }}>
                {radioStatus.power_on ? 'ON' : 'OFF'}
              </div>
            </div>

            <div style={styles.statusItem}>
              <div style={styles.statusLabel}>RF Gain</div>
              <div style={styles.statusValue}>{radioStatus.rf_gain}</div>
            </div>
          </div>

          <button
            onClick={getStatus}
            style={{
              ...styles.button,
              ...styles.buttonPrimary,
              width: '100%',
              marginTop: '20px'
            }}
          >
            <RefreshCw size={20} />
            Actualiser
          </button>
        </div>

        {/* Carte de scan */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <Radio size={24} />
            Scan de Fréquences
          </h2>

          <div style={styles.formGroup}>
            <label style={styles.label}>Fréquence de début (Hz)</label>
            <input
              type="number"
              value={scanStartFreq}
              onChange={(e) => setScanStartFreq(parseInt(e.target.value))}
              style={styles.input}
              placeholder="144000000"
            />
          </div>

          <div style={styles.formGroup}>
            <label style={styles.label}>Fréquence de fin (Hz)</label>
            <input
              type="number"
              value={scanEndFreq}
              onChange={(e) => setScanEndFreq(parseInt(e.target.value))}
              style={styles.input}
              placeholder="146000000"
            />
          </div>

          <div style={styles.formGroup}>
            <label style={styles.label}>Pas (Hz)</label>
            <input
              type="number"
              value={scanStep}
              onChange={(e) => setScanStep(parseInt(e.target.value))}
              style={styles.input}
              placeholder="25000"
            />
          </div>

          <div style={styles.buttonGrid}>
            <button
              onClick={startScan}
              disabled={loading}
              style={{...styles.button, ...styles.buttonSuccess}}
            >
              <Play size={20} />
              Démarrer
            </button>

            <button
              onClick={stopScan}
              style={{...styles.button, ...styles.buttonDanger}}
            >
              <Square size={20} />
              Arrêter
            </button>
          </div>
        </div>

        {/* Carte d'enregistrement audio */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <Volume2 size={24} />
            Enregistrement Audio
          </h2>

          <div style={styles.formGroup}>
            <label style={styles.label}>Type d'audio</label>
            <select
              value={audioType}
              onChange={(e) => setAudioType(e.target.value)}
              style={styles.select}
            >
              <option value="AF">AF (Audio Frequency)</option>
              <option value="IF">IF (Intermediate Frequency)</option>
            </select>
          </div>

          <div style={styles.buttonGrid}>
            <button
              onClick={startRecording}
              disabled={isRecording}
              style={{
                ...styles.button,
                ...styles.buttonSuccess,
                opacity: isRecording ? 0.5 : 1
              }}
            >
              <Play size={20} />
              Enregistrer
            </button>

            <button
              onClick={stopRecording}
              disabled={!isRecording}
              style={{
                ...styles.button,
                ...styles.buttonDanger,
                opacity: !isRecording ? 0.5 : 1
              }}
            >
              <Square size={20} />
              Arrêter
            </button>
          </div>

          {isRecording && (
            <div style={{
              ...styles.alert,
              backgroundColor: '#fff3cd',
              borderColor: '#ffeaa7',
              color: '#856404',
              marginTop: '15px'
            }}>
              🔴 Enregistrement en cours...
            </div>
          )}
        </div>

        {/* Carte des enregistrements */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <Download size={24} />
            Enregistrements ({recordings.length})
          </h2>

          <div style={{
            maxHeight: '300px',
            overflowY: 'auto',
            border: '2px solid #e9ecef',
            borderRadius: '8px',
            padding: '10px'
          }}>
            {recordings.length === 0 ? (
              <div style={{ textAlign: 'center', color: '#6c757d', padding: '20px' }}>
                Aucun enregistrement disponible
              </div>
            ) : (
              recordings.map((recording, index) => (
                <div key={index} style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '10px',
                  borderBottom: index < recordings.length - 1 ? '1px solid #e9ecef' : 'none',
                  marginBottom: '10px'
                }}>
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: '600', marginBottom: '5px' }}>
                      {recording.filename}
                    </div>
                    <div style={{ fontSize: '0.9rem', color: '#6c757d' }}>
                      {formatDuration(recording.duration)} • {(recording.size / 1024 / 1024).toFixed(1)} MB
                    </div>
                  </div>

                  <div style={{ display: 'flex', gap: '10px' }}>
                    <a
                      href={`${API_BASE_URL}/api/recordings/${recording.filename}`}
                      download
                      style={{
                        ...styles.button,
                        ...styles.buttonPrimary,
                        textDecoration: 'none',
                        padding: '8px 12px'
                      }}
                    >
                      <Download size={16} />
                    </a>

                    <button
                      onClick={() => deleteRecording(recording.filename)}
                      style={{
                        ...styles.button,
                        ...styles.buttonDanger,
                        padding: '8px 12px'
                      }}
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>

          <button
            onClick={loadRecordings}
            style={{
              ...styles.button,
              ...styles.buttonPrimary,
              width: '100%',
              marginTop: '15px'
            }}
          >
            <RefreshCw size={20} />
            Actualiser la liste
          </button>
        </div>
      </div>
    </div>
  );
};

export default App;
