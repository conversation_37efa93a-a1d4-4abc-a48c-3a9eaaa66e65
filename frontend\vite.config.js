import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// Configuration optimisée pour VirtualHere USB Server
export default defineConfig({
  plugins: [react({
    // Optimisations React pour performance
    fastRefresh: true
  })],

  // Configuration pour éviter les erreurs 404
  publicDir: 'public',

  // Configuration serveur pour VirtualHere USB
  server: {
    host: '0.0.0.0',
    port: 5173,
    strictPort: true,
    open: true,
    cors: true,
    hmr: {
      port: 5174,  // Port séparé pour HMR
      overlay: false  // Désactiver overlay d'erreur pour performance
    },
    proxy: {
      '/api': {
        target: 'http://*************:8000',
        changeOrigin: true,
        secure: false,
        ws: true,  // Support WebSocket
        timeout: 10000  // Timeout plus long pour VirtualHere
      },
      '/ws': {
        target: 'ws://*************:8000',
        ws: true,
        changeOrigin: true,
        timeout: 10000
      }
    }
  },

  // Optimisations build ultra-rapides
  build: {
    target: 'esnext',
    minify: 'esbuild',
    sourcemap: false,
    outDir: 'dist',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['lucide-react']
        }
      }
    }
  },

  // Optimisations développement pour VirtualHere
  optimizeDeps: {
    include: ['react', 'react-dom', 'lucide-react', 'axios'],
    force: true
  },

  // Configuration pour VirtualHere USB Server
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
    __VIRTUALHERE_MODE__: JSON.stringify(true),
    __API_BASE_URL__: JSON.stringify('http://*************:8000')
  },

  // Gestion des erreurs et logs
  logLevel: 'info',
  clearScreen: false
})
