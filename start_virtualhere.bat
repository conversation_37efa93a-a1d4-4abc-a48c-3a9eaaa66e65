@echo off
echo ========================================
echo DEMARRAGE APPLICATION VIRTUALHERE USB
echo ========================================
echo.

REM Vérifier si Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installé ou pas dans le PATH
    pause
    exit /b 1
)

REM Vérifier si Node.js est installé
node --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Node.js n'est pas installé ou pas dans le PATH
    pause
    exit /b 1
)

echo 1. Test de la configuration VirtualHere...
python test_virtualhere.py
if errorlevel 1 (
    echo.
    echo ERREUR: Tests VirtualHere échoués
    echo Vérifiez votre configuration avant de continuer
    pause
    exit /b 1
)

echo.
echo 2. Installation des dépendances Python...
pip install -r requirements.txt

echo.
echo 3. Installation des dépendances Node.js...
cd frontend
npm install
cd ..

echo.
echo 4. Démarrage du serveur backend...
start "Backend API" cmd /k "python backend/main.py"

echo.
echo 5. Attente du démarrage du backend...
timeout /t 5 /nobreak >nul

echo.
echo 6. Démarrage du serveur frontend...
cd frontend
start "Frontend Vite" cmd /k "npm run dev"
cd ..

echo.
echo ========================================
echo APPLICATION DEMARREE AVEC SUCCES !
echo ========================================
echo.
echo Backend API: http://*************:8000
echo Frontend:    http://*************:5173
echo.
echo Les deux serveurs sont maintenant en cours d'exécution.
echo Fermez cette fenêtre pour arrêter l'application.
echo.
pause
