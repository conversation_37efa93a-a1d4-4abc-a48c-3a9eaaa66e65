#!/usr/bin/env python3
"""
Script de test pour vérifier la configuration VirtualHere USB Server
Teste la communication avec l'IC-R8600 via VirtualHere
"""

import serial
import time
import json
import socket
import subprocess
import sys
from pathlib import Path

def load_config():
    """Charge la configuration depuis config.json"""
    try:
        with open("config.json", "r") as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Erreur lecture config: {e}")
        return None

def test_network_connectivity():
    """Test la connectivité réseau vers le PC VirtualHere"""
    print("🔍 Test de connectivité réseau...")
    
    # Test ping vers le PC VirtualHere (************)
    try:
        result = subprocess.run(
            ["ping", "-n", "3", "************"], 
            capture_output=True, 
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ Ping vers ************ réussi")
            return True
        else:
            print("❌ Ping vers ************ échoué")
            print(f"Sortie: {result.stdout}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test ping: {e}")
        return False

def check_virtualhere_client():
    """Vérifie si VirtualHere Client est en cours d'exécution"""
    print("🔍 Vérification VirtualHere Client...")
    
    try:
        result = subprocess.run(
            ["tasklist", "/FI", "IMAGENAME eq vhui64.exe"],
            capture_output=True,
            text=True
        )
        
        if "vhui64.exe" in result.stdout:
            print("✅ VirtualHere Client détecté")
            return True
        else:
            print("⚠️ VirtualHere Client non détecté")
            print("Assurez-vous que VirtualHere Client est démarré")
            return False
            
    except Exception as e:
        print(f"❌ Erreur vérification VirtualHere: {e}")
        return False

def test_com_port_availability():
    """Test la disponibilité du port COM6 via VirtualHere"""
    print("🔍 Test disponibilité port COM6...")
    
    try:
        # Essayer d'ouvrir le port COM6
        ser = serial.Serial(
            port="COM6",
            baudrate=19200,
            bytesize=serial.EIGHTBITS,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            timeout=2.0
        )
        
        print("✅ Port COM6 accessible via VirtualHere")
        ser.close()
        return True
        
    except serial.SerialException as e:
        print(f"❌ Port COM6 non accessible: {e}")
        print("Vérifiez que:")
        print("  - VirtualHere Client est connecté")
        print("  - Le port USB est partagé sur le serveur")
        print("  - Le port COM6 est bien mappé")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def test_icom_communication():
    """Test la communication avec l'IC-R8600 via VirtualHere"""
    print("🔍 Test communication IC-R8600...")
    
    try:
        ser = serial.Serial(
            port="COM6",
            baudrate=19200,
            bytesize=serial.EIGHTBITS,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            timeout=3.0  # Timeout plus long pour VirtualHere
        )
        
        # Vider le buffer
        ser.flushInput()
        ser.flushOutput()
        time.sleep(0.5)
        
        # Test 1: Ping CI-V
        print("  📡 Test ping CI-V...")
        ping_cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0xFD])
        ser.write(ping_cmd)
        time.sleep(1.0)  # Délai plus long pour VirtualHere
        
        response = ser.read(50)
        if response:
            print(f"  ✅ Réponse ping: {' '.join([f'{b:02X}' for b in response])}")
        else:
            print("  ⚠️ Pas de réponse au ping")
        
        # Test 2: Lecture fréquence
        print("  📡 Test lecture fréquence...")
        freq_cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0x03, 0xFD])
        ser.write(freq_cmd)
        time.sleep(1.0)
        
        response = ser.read(50)
        if response:
            print(f"  ✅ Réponse fréquence: {' '.join([f'{b:02X}' for b in response])}")
            success = True
        else:
            print("  ❌ Pas de réponse fréquence")
            success = False
        
        ser.close()
        return success
        
    except Exception as e:
        print(f"❌ Erreur communication IC-R8600: {e}")
        return False

def test_api_server():
    """Test si le serveur API est accessible"""
    print("🔍 Test serveur API...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('*************', 8000))
        sock.close()
        
        if result == 0:
            print("✅ Serveur API accessible sur *************:8000")
            return True
        else:
            print("❌ Serveur API non accessible")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test API: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("=" * 60)
    print("🧪 TEST CONFIGURATION VIRTUALHERE USB SERVER")
    print("=" * 60)
    
    config = load_config()
    if not config:
        print("❌ Impossible de charger la configuration")
        return False
    
    print(f"Configuration chargée:")
    print(f"  - Port série: {config['icom']['serial_port']}")
    print(f"  - Baud rate: {config['icom']['baudrate']}")
    print(f"  - VirtualHere activé: {config['icom'].get('virtualhere_enabled', False)}")
    print()
    
    # Tests séquentiels
    tests = [
        ("Connectivité réseau", test_network_connectivity),
        ("VirtualHere Client", check_virtualhere_client),
        ("Port COM6", test_com_port_availability),
        ("Communication IC-R8600", test_icom_communication),
        ("Serveur API", test_api_server)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}...")
        result = test_func()
        results.append((test_name, result))
        
        if not result:
            print(f"⚠️ Test '{test_name}' échoué")
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nRésultat: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Configuration VirtualHere parfaitement fonctionnelle !")
        print("\nVous pouvez maintenant:")
        print("  1. Démarrer le backend: python backend/main.py")
        print("  2. Démarrer le frontend: cd frontend && npm run dev")
    else:
        print("⚠️ Certains tests ont échoué. Vérifiez la configuration.")
        
        if not results[0][1]:  # Connectivité réseau
            print("\n🔧 SOLUTION: Vérifiez la connexion RJ45 entre les PC")
        if not results[1][1]:  # VirtualHere Client
            print("\n🔧 SOLUTION: Démarrez VirtualHere Client")
        if not results[2][1]:  # Port COM6
            print("\n🔧 SOLUTION: Vérifiez le partage USB dans VirtualHere")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
